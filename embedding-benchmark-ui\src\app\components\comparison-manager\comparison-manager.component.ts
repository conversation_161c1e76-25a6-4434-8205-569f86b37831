import { Component, OnInit, OnDestroy, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';
import { BenchmarkApiService } from '../../services/benchmark-api.service';
import { ComparisonService } from '../../services/comparison.service';
import { BenchmarkResult, ComparisonResult } from '../../models/benchmark.models';

@Component({
  selector: 'app-comparison-manager',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './comparison-manager.component.html',
  styleUrls: ['./comparison-manager.component.scss']
})
export class ComparisonManagerComponent implements OnInit, OnDestroy {
  @Input() currentResult: BenchmarkResult | null = null;
  @Output() comparisonModeChange = new EventEmitter<boolean>();

  savedResults: BenchmarkResult[] = [];
  selectedResultId: string = '';
  isComparisonMode = false;
  currentComparison: ComparisonResult | null = null;
  isLoading = false;
  error: string | null = null;

  private destroy$ = new Subject<void>();

  constructor(
    private benchmarkApi: BenchmarkApiService,
    private comparisonService: ComparisonService
  ) {}

  ngOnInit(): void {
    this.loadSavedResults();
    
    this.comparisonService.isComparisonMode$
      .pipe(takeUntil(this.destroy$))
      .subscribe(isEnabled => {
        this.isComparisonMode = isEnabled;
        this.comparisonModeChange.emit(isEnabled);
      });

    // Subscribe to comparison results
    this.comparisonService.comparisonResult$
      .pipe(takeUntil(this.destroy$))
      .subscribe(comparison => {
        this.currentComparison = comparison;
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadSavedResults(): void {
    this.isLoading = true;
    this.error = null;

    this.benchmarkApi.getAllBenchmarkResults().subscribe({
      next: (results) => {
        this.savedResults = results.filter(r => r.status === 'COMPLETED');
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading saved results:', error);
        this.error = 'Failed to load saved results';
        this.isLoading = false;
      }
    });
  }

  toggleComparisonMode(): void {
    const newMode = !this.isComparisonMode;
    this.comparisonService.setComparisonMode(newMode);
    
    if (!newMode) {
      this.selectedResultId = '';
    }
  }

  onResultSelect(): void {
    if (!this.selectedResultId || !this.currentResult) {
      return;
    }

    const selectedResult = this.savedResults.find(r => r.jobId === this.selectedResultId);
    if (!selectedResult) {
      this.error = 'Selected result not found';
      return;
    }

    this.error = null;
    this.comparisonService.compareResults(this.currentResult, selectedResult);
  }

  saveCurrentResult(): void {
    if (!this.currentResult) {
      return;
    }

    const label = prompt('Enter a label for this result:');
    if (!label || label.trim() === '') {
      return;
    }

    this.benchmarkApi.saveBenchmarkResult(this.currentResult.jobId, label.trim()).subscribe({
      next: () => {
        this.loadSavedResults(); // Refresh the list
      },
      error: (error) => {
        console.error('Error saving result:', error);
        this.error = 'Failed to save result';
      }
    });
  }

  deleteResult(jobId: string): void {
    if (!confirm('Are you sure you want to delete this result?')) {
      return;
    }

    this.benchmarkApi.deleteBenchmarkResult(jobId).subscribe({
      next: () => {
        this.loadSavedResults(); // Refresh the list
        
        // Clear comparison if the deleted result was being compared
        if (this.selectedResultId === jobId) {
          this.selectedResultId = '';
          this.comparisonService.clearComparison();
        }
      },
      error: (error) => {
        console.error('Error deleting result:', error);
        this.error = 'Failed to delete result';
      }
    });
  }

  clearComparison(): void {
    this.selectedResultId = '';
    this.comparisonService.clearComparison();
  }

  getResultDisplayName(result: BenchmarkResult): string {
    if (result.indexName) {
      return result.indexName;
    }
    
    const modelName = result.modelInfo?.name || result.modelInfo?.modelName || 'Unknown Model';
    const timestamp = result.createdAt ? new Date(result.createdAt).toLocaleString() : result.jobId.substring(0, 8);
    
    return `${modelName} - ${timestamp}`;
  }

  getResultSummary(result: BenchmarkResult): string {
    if (!result.overallMetrics) {
      return 'No metrics available';
    }

    const precision = (result.overallMetrics.avgPrecision * 100).toFixed(1);
    const recall = (result.overallMetrics.avgRecall * 100).toFixed(1);
    const ndcg = (result.overallMetrics.avgNdcg * 100).toFixed(1);

    return `P: ${precision}%, R: ${recall}%, nDCG: ${ndcg}%`;
  }

  formatDate(dateString: string): string {
    try {
      return new Date(dateString).toLocaleString();
    } catch {
      return dateString;
    }
  }

  getSelectedResult(): BenchmarkResult | null {
    if (!this.selectedResultId) {
      return null;
    }
    return this.savedResults.find(r => r.jobId === this.selectedResultId) || null;
  }
}
