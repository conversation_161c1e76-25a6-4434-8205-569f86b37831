.results-comparison {
  .comparison-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e5e7eb;

    .title {
      margin: 0;
      font-size: 1.5rem;
      font-weight: 700;
      color: #111827;
    }

    .btn-download {
      padding: 0.5rem 1rem;
      background: #3b82f6;
      color: white;
      border: none;
      border-radius: 0.375rem;
      font-size: 0.875rem;
      cursor: pointer;
      transition: background 0.2s;

      &:hover {
        background: #2563eb;
      }

      i {
        margin-right: 0.5rem;
      }
    }
  }

  .models-overview {
    display: grid;
    gap: 2rem;
    margin-bottom: 2rem;
    align-items: center;

    .model-card {
      padding: 1.5rem;
      border-radius: 0.75rem;
      border: 2px solid;

      &.primary {
        background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
        border-color: #3b82f6;
      }

      &.secondary {
        background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
        border-color: #10b981;
      }

      .model-header {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 0.75rem;

        .model-badge {
          padding: 0.25rem 0.75rem;
          border-radius: 1rem;
          font-size: 0.75rem;
          font-weight: 700;
          text-transform: uppercase;
          letter-spacing: 0.05em;
        }

        .model-name {
          margin: 0;
          font-size: 1.25rem;
          font-weight: 600;
          color: #111827;
        }
      }

      &.primary .model-badge {
        background: #3b82f6;
        color: white;
      }

      &.secondary .model-badge {
        background: #10b981;
        color: white;
      }

      .model-meta {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
        font-size: 0.875rem;
        color: #6b7280;

        .model-label {
          font-weight: 500;
          color: #374151;
        }

        .model-id {
          font-family: monospace;
          font-size: 0.75rem;
        }
      }
    }

    .vs-divider {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 4rem;
      height: 4rem;
      background: #f3f4f6;
      border: 2px solid #d1d5db;
      border-radius: 50%;
      font-weight: 700;
      font-size: 1.125rem;
      color: #6b7280;
    }
  }

  .overall-comparison {
    margin-bottom: 2rem;

    .section-title {
      margin: 0 0 1.5rem 0;
      font-size: 1.25rem;
      font-weight: 600;
      color: #111827;
    }

    .metrics-comparison-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 1rem;

      .metric-comparison {
        padding: 1rem;
        background: white;
        border: 1px solid #e5e7eb;
        border-radius: 0.5rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

        .metric-name {
          font-weight: 600;
          color: #374151;
          margin-bottom: 0.75rem;
          font-size: 0.875rem;
          text-transform: uppercase;
          letter-spacing: 0.05em;
        }

        .metric-values {
          display: flex;
          justify-content: space-between;
          margin-bottom: 0.5rem;
          font-size: 1.125rem;
          font-weight: 600;

          .primary-value {
            color: #3b82f6;
          }

          .secondary-value {
            color: #10b981;
          }
        }

        .metric-difference {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          font-size: 0.875rem;
          font-weight: 600;

          &.trend-positive {
            color: #059669;
          }

          &.trend-negative {
            color: #dc2626;
          }

          &.trend-neutral {
            color: #6b7280;
          }

          i {
            font-size: 0.75rem;
          }
        }
      }
    }
  }

  .query-comparison-section {
    .section-title {
      margin: 0 0 1.5rem 0;
      font-size: 1.25rem;
      font-weight: 600;
      color: #111827;
    }

    .query-list {
      .query-item {
        margin-bottom: 1rem;
        border: 1px solid #e5e7eb;
        border-radius: 0.5rem;
        background: white;
        overflow: hidden;

        .query-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 1rem;
          cursor: pointer;
          transition: background 0.2s;

          &:hover {
            background: #f9fafb;
          }

          .query-info {
            flex: 1;

            .query-text {
              display: block;
              font-weight: 500;
              color: #111827;
              margin-bottom: 0.5rem;
            }

            .query-summary {
              display: flex;
              gap: 1rem;
              font-size: 0.75rem;
              color: #6b7280;

              .metric-summary {
                font-family: monospace;
              }
            }
          }

          .query-controls {
            color: #6b7280;
            font-size: 1.25rem;
          }
        }

        .query-details {
          border-top: 1px solid #e5e7eb;
          background: #f9fafb;

          .detailed-metrics {
            padding: 1rem;

            .metrics-table {
              .table-header,
              .table-row {
                display: grid;
                grid-template-columns: 1fr 1fr 1fr 1fr;
                gap: 1rem;
                padding: 0.5rem 0;
                font-size: 0.875rem;
              }

              .table-header {
                font-weight: 600;
                color: #374151;
                border-bottom: 1px solid #d1d5db;
                margin-bottom: 0.5rem;
              }

              .table-row {
                border-bottom: 1px solid #e5e7eb;

                &:last-child {
                  border-bottom: none;
                }

                .primary-col {
                  color: #3b82f6;
                  font-weight: 500;
                }

                .secondary-col {
                  color: #10b981;
                  font-weight: 500;
                }

                .diff-col {
                  font-weight: 600;

                  &.trend-positive {
                    color: #059669;
                  }

                  &.trend-negative {
                    color: #dc2626;
                  }

                  &.trend-neutral {
                    color: #6b7280;
                  }
                }
              }
            }
          }

          .ranking-changes {
            padding: 1rem;
            border-top: 1px solid #e5e7eb;

            h5 {
              margin: 0 0 0.75rem 0;
              font-size: 0.875rem;
              font-weight: 600;
              color: #374151;
              text-transform: uppercase;
              letter-spacing: 0.05em;
            }

            .ranking-changes-list {
              .ranking-change-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0.5rem 0;
                border-bottom: 1px solid #e5e7eb;
                font-size: 0.875rem;

                &:last-child {
                  border-bottom: none;
                }

                .document-id {
                  font-family: monospace;
                  color: #374151;
                  font-weight: 500;
                }

                .ranking-change {
                  display: flex;
                  align-items: center;
                  gap: 0.5rem;
                  color: #6b7280;

                  i {
                    font-size: 0.75rem;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

.no-comparison {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
  color: #6b7280;

  i {
    color: #9ca3af;
    margin-bottom: 1rem;
  }

  p {
    margin: 0;
    font-size: 1.125rem;
  }
}

// Responsive design
@media (max-width: 768px) {
  .results-comparison {
    .models-overview {
      grid-template-columns: 1fr;
      gap: 1rem;

      .vs-divider {
        width: 3rem;
        height: 3rem;
        font-size: 1rem;
        justify-self: center;
      }
    }

    .overall-comparison .metrics-comparison-grid {
      grid-template-columns: 1fr;
    }

    .query-comparison-section .query-list .query-item .query-details .detailed-metrics .metrics-table {
      .table-header,
      .table-row {
        grid-template-columns: 1fr;
        gap: 0.5rem;
        text-align: left;
      }

      .table-header {
        display: none;
      }

      .table-row {
        display: block;
        padding: 0.75rem;
        margin-bottom: 0.5rem;
        background: white;
        border-radius: 0.25rem;

        > div {
          margin-bottom: 0.25rem;

          &:before {
            content: attr(class);
            font-weight: 600;
            margin-right: 0.5rem;
            text-transform: capitalize;
          }
        }
      }
    }
  }
}
