package os.tukan.extractor.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.stereotype.Service;
import os.tukan.extractor.config.EmbeddingModelConfig;
import os.tukan.extractor.config.OllamaConfig;
import os.tukan.extractor.dto.OllamaModelResponse;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.util.List;
import java.util.stream.Collectors;
import java.time.Duration;

@Service
public class OllamaModelDiscoveryService {

    private final OllamaConfig ollamaConfig;
    private final HttpClient httpClient;
    private final ObjectMapper objectMapper;

    public OllamaModelDiscoveryService(OllamaConfig ollamaConfig) {
        this.ollamaConfig = ollamaConfig;
        this.httpClient = HttpClient.newBuilder()
                .connectTimeout(Duration.ofSeconds(30))
                .build();
        this.objectMapper = new ObjectMapper();
    }

    public List<EmbeddingModelConfig> getAvailableEmbeddingModels() throws Exception {
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(ollamaConfig.getTagsEndpoint()))
                .timeout(Duration.ofSeconds(30))
                .GET()
                .build();

        try {
            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());

            if (response.statusCode() != 200) {
                throw new Exception("Ollama API returned status: " + response.statusCode());
            }

            OllamaModelResponse ollamaResponse = objectMapper.readValue(response.body(), OllamaModelResponse.class);

            return ollamaResponse.getModels().stream()
                    .map(model -> {
                        EmbeddingModelConfig config = new EmbeddingModelConfig();
                        config.setId(model.getName().toLowerCase().replaceAll("[^a-z0-9]", "-"));
                        config.setName(model.getName());
                        config.setProvider("ollama");
                        config.setEndpoint(ollamaConfig.getEmbedEndpoint());
                        config.setModelName(model.getName());
                        return config;
                    })
                    .collect(Collectors.toList());

        } catch (Exception e) {
            throw new Exception("Failed to connect to Ollama server at " + ollamaConfig.getTagsEndpoint() + ": " + e.getMessage(), e);
        }
    }

    public String generateIndexName(String modelName, String similarityFunction) {
        String modelRoot = "unknown";
        if (modelName != null && !modelName.isEmpty()) {
            if (modelName.contains("/")) {
                modelRoot = modelName.split("/")[0];
            } else {
                modelRoot = modelName.split("[:\\-]")[0];
            }
        }

        String sim = similarityFunction == null ? "unknown"
                : similarityFunction.toLowerCase().replaceAll("[^a-z0-9]", "");

        return modelRoot.toLowerCase() + "-" + sim;
    }
}
