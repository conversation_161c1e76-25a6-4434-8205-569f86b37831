package os.tukan.extractor.controller;

import lombok.extern.log4j.Log4j2;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import os.tukan.extractor.config.BenchmarkConfig;
import os.tukan.extractor.config.EmbeddingModelConfig;
import os.tukan.extractor.model.BenchmarkRequest;
import os.tukan.extractor.model.BenchmarkResult;
import os.tukan.extractor.model.ComparisonResult;
import os.tukan.extractor.service.BenchmarkService;
import os.tukan.extractor.service.FileProcessingService;
import os.tukan.extractor.service.GroundTruthProcessingService;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Log4j2
@RestController
@RequestMapping("/api/benchmark")
@CrossOrigin(origins = "http://localhost:4200")
public class BenchmarkController {

    private final BenchmarkService benchmarkService;
    private final FileProcessingService fileProcessingService;
    private final GroundTruthProcessingService groundTruthProcessingService;
    private final BenchmarkConfig benchmarkConfig;

    public BenchmarkController(BenchmarkService benchmarkService,
            FileProcessingService fileProcessingService,
            GroundTruthProcessingService groundTruthProcessingService,
            BenchmarkConfig benchmarkConfig) {
        this.benchmarkService = benchmarkService;
        this.fileProcessingService = fileProcessingService;
        this.groundTruthProcessingService = groundTruthProcessingService;
        this.benchmarkConfig = benchmarkConfig;
    }

    private ResponseEntity<Map<String, Object>> validateFileNotEmpty(MultipartFile file) {
        if (file.isEmpty()) {
            return ResponseEntity.badRequest().body(Map.of("error", "No file was provided"));
        }
        return null;
    }

    private ResponseEntity<Map<String, Object>> handleException(Exception e) {
        return ResponseEntity.internalServerError().body(Map.of("error", e.getMessage()));
    }

    private ResponseEntity<Map<String, String>> validateBenchmarkRequest(BenchmarkRequest request) {
        if (request.getModelId() == null || request.getModelId().isEmpty()) {
            return ResponseEntity.badRequest().body(Map.of("error", "Model ID is required"));
        }
        if (request.getDatasetId() == null || request.getDatasetId().isEmpty()) {
            return ResponseEntity.badRequest().body(Map.of("error", "Dataset ID is required"));
        }
        if (request.getQueries() == null || request.getQueries().isEmpty()) {
            return ResponseEntity.badRequest().body(Map.of("error", "Queries are required"));
        }
        return null;
    }

    private long calculateTotalFileSize(MultipartFile[] files) {
        if (files == null || files.length == 0) {
            return 0L;
        }
        return Arrays.stream(files)
            .filter(file -> file != null && !file.isEmpty())
            .mapToLong(MultipartFile::getSize)
            .sum();
    }

    @GetMapping("/models")
    public ResponseEntity<List<EmbeddingModelConfig>> getAvailableModels() {
        try {
            List<EmbeddingModelConfig> models = benchmarkService.getAvailableModels();
            return ResponseEntity.ok(models);
        } catch (Exception e) {
            log.error("Error in getAvailableModels endpoint", e);
            return ResponseEntity.status(503).build();
        }
    }

    @PostMapping("/models/refresh")
    public ResponseEntity<List<EmbeddingModelConfig>> refreshAvailableModels() {
        try {
            benchmarkService.clearModelCache();
            List<EmbeddingModelConfig> models = benchmarkService.getAvailableModels();
            return ResponseEntity.ok(models);
        } catch (Exception e) {
            log.error("Error in refreshAvailableModels endpoint", e);
            return ResponseEntity.status(503).build();
        }
    }

    @GetMapping("/debug/ollama")
    public ResponseEntity<Map<String, Object>> debugOllama() {
        try {
            List<EmbeddingModelConfig> allModels = benchmarkService.getAvailableModels();
            return ResponseEntity.ok(Map.of(
                "status", "success",
                "modelCount", allModels.size(),
                "models", allModels
            ));
        } catch (Exception e) {
            log.error("Debug Ollama endpoint error", e);
            return ResponseEntity.ok(Map.of(
                "status", "error",
                "error", e.getMessage()
            ));
        }
    }

    @PostMapping("/upload/dataset")
    public ResponseEntity<Map<String, Object>> uploadDataset(@RequestParam("file") MultipartFile file) {
        try {
            ResponseEntity<Map<String, Object>> validationError = validateFileNotEmpty(file);
            if (validationError != null) {
                return validationError;
            }

            // Calculate dataset size before processing
            long datasetSizeBytes = file.getSize();

            List<String> documents = fileProcessingService.processDatasetFile(file);
            String datasetId = benchmarkService.storeDataset(documents, datasetSizeBytes);

            return ResponseEntity.ok(Map.of(
                    "datasetId", datasetId,
                    "documentCount", documents.size(),
                    "datasetSizeBytes", datasetSizeBytes,
                    "message", "Dataset uploaded successfully"));

        } catch (Exception e) {
            return handleException(e);
        }
    }

    @PostMapping("/upload/dataset/multiple")
    public ResponseEntity<Map<String, Object>> uploadMultipleDatasets(
            @RequestParam("files") MultipartFile[] files,
            @RequestParam(value = "directoryMode", defaultValue = "false") boolean directoryMode) {
        try {
            if (files == null || files.length == 0) {
                return ResponseEntity.badRequest().body(Map.of("error", "No files were provided"));
            }

            // Calculate total dataset size before processing
            long datasetSizeBytes = calculateTotalFileSize(files);

            // Process files with appropriate mode
            List<String> documents = fileProcessingService.processMultipleDatasetFiles(files, directoryMode);
            String datasetId = benchmarkService.storeDataset(documents, datasetSizeBytes);

            // Build response based on mode
            Map<String, Object> response = new HashMap<>();
            response.put("datasetId", datasetId);
            response.put("documentCount", documents.size());
            response.put("fileCount", files.length);
            response.put("datasetSizeBytes", datasetSizeBytes);

            if (directoryMode) {
                // Count PDF files for directory mode response
                int pdfFileCount = (int) Arrays.stream(files)
                    .filter(file -> file != null && !file.isEmpty() && file.getOriginalFilename() != null)
                    .filter(file -> file.getOriginalFilename().toLowerCase().endsWith(".pdf"))
                    .count();

                response.put("pdfFileCount", pdfFileCount);
                response.put("message", String.format("Successfully processed %d PDF files from directory (%d total files) with %d total documents",
                        pdfFileCount, files.length, documents.size()));
            } else {
                response.put("message", String.format("Successfully processed %d files with %d total documents",
                        files.length, documents.size()));
            }

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            return handleException(e);
        }
    }

    @PostMapping("/upload/dataset/directory")
    @Deprecated
    public ResponseEntity<Map<String, Object>> uploadDirectoryDataset(@RequestParam("files") MultipartFile[] files) {
        // Redirect to unified endpoint with directoryMode=true for backward compatibility
        return uploadMultipleDatasets(files, true);
    }

    @PostMapping("/upload/groundtruth")
    public ResponseEntity<Map<String, Object>> uploadGroundTruth(@RequestParam("file") MultipartFile file) {
        try {
            ResponseEntity<Map<String, Object>> validationError = validateFileNotEmpty(file);
            if (validationError != null) {
                return validationError;
            }

            GroundTruthProcessingService.GroundTruthResult result = groundTruthProcessingService.processGroundTruthFile(file);

            return ResponseEntity.ok(Map.of(
                    "groundTruth", result.getGroundTruth(),
                    "queries", result.getQueries(),
                    "queryCount", result.getQueries().size(),
                    "message", "Ground truth and queries uploaded successfully"));

        } catch (Exception e) {
            return handleException(e);
        }
    }

    @PostMapping("/run")
    public ResponseEntity<Map<String, String>> startBenchmark(@RequestBody BenchmarkRequest request) {
        try {
            // Validate request
            ResponseEntity<Map<String, String>> validationError = validateBenchmarkRequest(request);
            if (validationError != null) {
                return validationError;
            }

            if (request.getSimilarityFunction() == null || request.getSimilarityFunction().isEmpty()) {
                request.setSimilarityFunction("cosine");
            }
            if (request.getTopK() <= 0) {
                request.setTopK(benchmarkConfig.getDefaultTopK());
            }

            String jobId = benchmarkService.startAsyncBenchmark(request);

            return ResponseEntity.ok(Map.of(
                    "jobId", jobId,
                    "message", "Benchmark started successfully"));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of("error", e.getMessage()));
        }
    }

    @GetMapping("/results/{jobId}")
    public ResponseEntity<BenchmarkResult> getBenchmarkResult(@PathVariable String jobId) {
        try {
            BenchmarkResult result = benchmarkService.getBenchmarkResult(jobId);

            if (result == null) {
                return ResponseEntity.notFound().build();
            }

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/similarity-functions")
    public ResponseEntity<List<String>> getSimilarityFunctions() {
        try {
            List<String> functions = benchmarkConfig.getSimilarityFunctions();
            return ResponseEntity.ok(functions);
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/results")
    public ResponseEntity<List<BenchmarkResult>> getAllBenchmarkResults() {
        try {
            List<BenchmarkResult> results = benchmarkService.getAllCompletedResults();
            return ResponseEntity.ok(results);
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }

    @PostMapping("/results/{jobId}/save")
    public ResponseEntity<Map<String, String>> saveBenchmarkResult(
            @PathVariable String jobId,
            @RequestBody Map<String, String> request) {
        try {
            String label = request.get("label");
            if (label == null || label.trim().isEmpty()) {
                return ResponseEntity.badRequest().body(Map.of("error", "Label is required"));
            }

            boolean saved = benchmarkService.saveBenchmarkResult(jobId, label.trim());
            if (!saved) {
                return ResponseEntity.status(404).body(Map.of("error", "Benchmark result not found"));
            }

            return ResponseEntity.ok(Map.of("message", "Benchmark result saved successfully"));
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of("error", e.getMessage()));
        }
    }

    @DeleteMapping("/results/{jobId}")
    public ResponseEntity<Map<String, String>> deleteBenchmarkResult(@PathVariable String jobId) {
        try {
            boolean deleted = benchmarkService.deleteBenchmarkResult(jobId);
            if (!deleted) {
                return ResponseEntity.status(404).body(Map.of("error", "Benchmark result not found"));
            }

            return ResponseEntity.ok(Map.of("message", "Benchmark result deleted successfully"));
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of("error", e.getMessage()));
        }
    }

    @GetMapping("/compare/{primaryJobId}/{secondaryJobId}")
    public ResponseEntity<?> compareHistoricalResults(
            @PathVariable String primaryJobId,
            @PathVariable String secondaryJobId) {
        try {
            ComparisonResult comparison = benchmarkService.compareHistoricalResults(primaryJobId, secondaryJobId);
            return ResponseEntity.ok(comparison);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(Map.of("error", e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of("error", e.getMessage()));
        }
    }

}