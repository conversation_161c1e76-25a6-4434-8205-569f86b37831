import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import {
  EmbeddingModelConfig,
  BenchmarkRequest,
  BenchmarkResult,
  UploadResponse,
  SavedBenchmarkResult,
  ComparisonResult
} from '../models/benchmark.models';

@Injectable({
  providedIn: 'root'
})
export class BenchmarkApiService {
  private readonly baseUrl = 'http://localhost:8080/api/benchmark';

  constructor(private http: HttpClient) {}

  getAvailableModels(): Observable<EmbeddingModelConfig[]> {
    return this.http.get<EmbeddingModelConfig[]>(`${this.baseUrl}/models`);
  }

  getSimilarityFunctions(): Observable<string[]> {
    return this.http.get<string[]>(`${this.baseUrl}/similarity-functions`);
  }

  uploadDataset(file: File): Observable<UploadResponse> {
    const formData = new FormData();
    formData.append('file', file);
    return this.http.post<UploadResponse>(`${this.baseUrl}/upload/dataset`, formData);
  }

  uploadMultipleDatasets(files: File[], directoryMode: boolean = false): Observable<UploadResponse> {
    const formData = new FormData();
    files.forEach(file => {
      formData.append('files', file);
    });

    if (directoryMode) {
      formData.append('directoryMode', 'true');
    }

    return this.http.post<UploadResponse>(`${this.baseUrl}/upload/dataset/multiple`, formData);
  }



  uploadGroundTruth(file: File): Observable<UploadResponse> {
    const formData = new FormData();
    formData.append('file', file);
    return this.http.post<UploadResponse>(`${this.baseUrl}/upload/groundtruth`, formData);
  }

  startBenchmark(request: BenchmarkRequest): Observable<{ jobId: string; message: string }> {
    return this.http.post<{ jobId: string; message: string }>(`${this.baseUrl}/run`, request);
  }

  getBenchmarkResult(jobId: string): Observable<BenchmarkResult> {
    return this.http.get<BenchmarkResult>(`${this.baseUrl}/results/${jobId}`);
  }

  getAllBenchmarkResults(): Observable<BenchmarkResult[]> {
    return this.http.get<BenchmarkResult[]>(`${this.baseUrl}/results`);
  }

  saveBenchmarkResult(jobId: string, label: string): Observable<{ message: string }> {
    return this.http.post<{ message: string }>(`${this.baseUrl}/results/${jobId}/save`, { label });
  }

  deleteBenchmarkResult(jobId: string): Observable<{ message: string }> {
    return this.http.delete<{ message: string }>(`${this.baseUrl}/results/${jobId}`);
  }

  compareHistoricalResults(primaryJobId: string, secondaryJobId: string): Observable<ComparisonResult> {
    return this.http.get<ComparisonResult>(`${this.baseUrl}/compare/${primaryJobId}/${secondaryJobId}`);
  }
}
