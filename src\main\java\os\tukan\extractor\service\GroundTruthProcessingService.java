package os.tukan.extractor.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Getter;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * Specialized service for processing ground truth files and query datasets.
 * Handles JSON-based ground truth data and query extraction.
 */
@Service
public class GroundTruthProcessingService {

    private final ObjectMapper objectMapper;

    public GroundTruthProcessingService() {
        this.objectMapper = new ObjectMapper();
    }



    /**
     * Process ground truth file - supports JSON format only
     * Expected formats:
     * 1. Simple: {"query1": ["doc1", "doc2"], "query2": ["doc3"]}
     * 2. Combined dataset: {"queries": [...], "ground_truth": {...}, ...}
     */
    public GroundTruthResult processGroundTruthFile(MultipartFile file) throws IOException {
        if (file == null || file.isEmpty() || file.getOriginalFilename() == null) {
            throw new IOException("Invalid ground truth file provided");
        }

        if (!file.getOriginalFilename().toLowerCase().endsWith(".json")) {
            throw new IOException("Ground truth file must be in JSON format");
        }

        try {
            String content = new String(file.getBytes(), StandardCharsets.UTF_8);
            JsonNode rootNode = objectMapper.readTree(content);

            // Check if this is a combined dataset format
            if (rootNode.has("queries") && rootNode.has("ground_truth")) {
                return processCombinedDatasetFormat(rootNode);
            } else {
                // Assume simple ground truth format
                return processSimpleGroundTruthFormat(content);
            }

        } catch (Exception e) {
            throw new IOException("Failed to parse ground truth JSON: " + e.getMessage(), e);
        }
    }

    private GroundTruthResult processCombinedDatasetFormat(JsonNode rootNode) throws IOException {
        try {
            // Extract queries
            JsonNode queriesNode = rootNode.get("queries");
            List<String> queries = new ArrayList<>();
            if (queriesNode.isArray()) {
                for (JsonNode queryNode : queriesNode) {
                    if (queryNode.isTextual()) {
                        queries.add(queryNode.asText());
                    }
                }
            }

            // Extract ground truth
            JsonNode groundTruthNode = rootNode.get("ground_truth");
            TypeReference<Map<String, List<String>>> typeRef = new TypeReference<>() {};
            Map<String, List<String>> groundTruth = objectMapper.convertValue(groundTruthNode, typeRef);

            return new GroundTruthResult(queries, groundTruth);

        } catch (Exception e) {
            throw new IOException("Failed to parse combined dataset format: " + e.getMessage(), e);
        }
    }

    private GroundTruthResult processSimpleGroundTruthFormat(String content) throws IOException {
        try {
            TypeReference<Map<String, List<String>>> typeRef = new TypeReference<>() {};
            Map<String, List<String>> groundTruth = objectMapper.readValue(content, typeRef);

            // Extract queries from ground truth keys
            List<String> queries = new ArrayList<>(groundTruth.keySet());

            return new GroundTruthResult(queries, groundTruth);

        } catch (Exception e) {
            throw new IOException("Failed to parse simple ground truth format: " + e.getMessage(), e);
        }
    }

    @Getter
    public static class GroundTruthResult {
        private final List<String> queries;
        private final Map<String, List<String>> groundTruth;

        public GroundTruthResult(List<String> queries, Map<String, List<String>> groundTruth) {
            this.queries = queries;
            this.groundTruth = groundTruth;
        }
    }


}
