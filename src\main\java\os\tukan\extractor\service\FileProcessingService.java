package os.tukan.extractor.service;

import org.apache.pdfbox.Loader;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PreDestroy;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * Specialized PDF processing service with advanced text chunking and parallel processing capabilities.
 * This service exclusively handles PDF files and provides optimized text extraction with content-aware segmentation.
 */
@Service
public class FileProcessingService {

    private static final int DEFAULT_CHUNK_SIZE = 1000;
    private static final int DEFAULT_OVERLAP = 100;
    private static final int MAX_CHUNK_SIZE = 2000;
    private static final int MIN_CHUNK_SIZE = 200;
    private static final long MAX_FILE_SIZE = 100L * 1024 * 1024; // 100MB

    private final ExecutorService executorService;

    public FileProcessingService() {
        this.executorService = Executors.newFixedThreadPool(
            Math.min(Runtime.getRuntime().availableProcessors(), 4)
        );
    }    /**
     * Process a single PDF dataset file with advanced content-aware chunking.
     * Only PDF files are supported by this specialized service.
     *
     * @param file The PDF file to process
     * @return List of text chunks extracted from the PDF
     * @throws IOException if the file is invalid or not a PDF
     */
    public List<String> processDatasetFile(MultipartFile file) throws IOException {
        if (file == null || file.isEmpty() || file.getOriginalFilename() == null) {
            throw new IOException("Invalid PDF file provided");
        }

        String extension = getFileExtension(file.getOriginalFilename()).toLowerCase();
        if (!"pdf".equals(extension)) {
            throw new IOException("Only PDF files are supported. Provided file type: " + extension);
        }

        return processPdfDataset(file);
    }    /**
     * Process multiple PDF files with high-performance parallel processing.
     * Only PDF files are supported by this specialized service.
     *
     * @param files Array of PDF files to process
     * @return List of text chunks from all processed PDF files
     * @throws IOException if no valid PDF files are provided
     */
    public List<String> processMultipleDatasetFiles(MultipartFile[] files) throws IOException {
        return processMultipleDatasetFiles(files, false);
    }

    /**
     * Process multiple PDF files with optional directory filtering and parallel processing.
     * Uses ExecutorService for efficient concurrent processing of large PDF collections.
     *
     * @param files Array of files to process
     * @param directoryMode If true, filters out non-PDF files (used for directory uploads)
     * @return List of text chunks from all processed PDF files
     * @throws IOException if no valid PDF files are found
     */
    public List<String> processMultipleDatasetFiles(MultipartFile[] files, boolean directoryMode) throws IOException {
        if (files == null || files.length == 0) {
            throw new IOException("No PDF files provided");
        }

        // Filter and validate PDF files
        List<MultipartFile> pdfFiles = Arrays.stream(files)
            .filter(file -> file != null && !file.isEmpty() && file.getOriginalFilename() != null)
            .filter(file -> {
                String extension = getFileExtension(file.getOriginalFilename()).toLowerCase();
                return "pdf".equals(extension);
            })
            .filter(file -> {
                if (file.getSize() > MAX_FILE_SIZE) {
                    // Skip oversized files but continue processing others
                    return false;
                }
                return true;
            })
            .collect(Collectors.toList());

        if (pdfFiles.isEmpty()) {
            if (directoryMode) {
                throw new IOException("No PDF files found in the selected directory. Only PDF files are supported.");
            } else {
                throw new IOException("No valid PDF files provided. Only PDF files are supported.");
            }
        }

        // Process PDFs in parallel using ExecutorService
        List<CompletableFuture<List<String>>> futures = pdfFiles.stream()
            .map(file -> CompletableFuture.supplyAsync(() -> {
                try {
                    List<String> documents = processPdfDataset(file);
                    String filename = getFileNameWithoutExtension(file.getOriginalFilename());
                    return documents.stream()
                        .map(doc -> String.format("[%s] %s", filename, doc))
                        .collect(Collectors.toList());
                } catch (Exception e) {
                    // Return empty list for failed files to continue processing others
                    return new ArrayList<String>();
                }
            }, executorService))
            .collect(Collectors.toList());

        // Collect results from all futures
        List<String> allDocuments = new ArrayList<>();
        for (CompletableFuture<List<String>> future : futures) {
            try {
                allDocuments.addAll(future.get(30, TimeUnit.SECONDS));
            } catch (Exception e) {
                // Continue processing other files if one fails
            }
        }

        if (allDocuments.isEmpty()) {
            throw new IOException("No PDF documents could be processed successfully");
        }

        return allDocuments;
    }



    /**
     * Process a PDF file with advanced content-aware chunking for optimal text segmentation.
     * Extracts full text and applies sophisticated chunking strategies for better document understanding.
     *
     * @param file The PDF file to process
     * @return List of optimally chunked text segments
     * @throws IOException if PDF processing fails
     */
    private List<String> processPdfDataset(MultipartFile file) throws IOException {
        try (InputStream inputStream = file.getInputStream();
             PDDocument document = Loader.loadPDF(inputStream.readAllBytes())) {

            PDFTextStripper textStripper = new PDFTextStripper();
            String fullText = textStripper.getText(document);

            return chunkTextContentAware(fullText);

        } catch (IOException e) {
            throw new IOException("Failed to process PDF: " + e.getMessage(), e);
        }
    }

    /**
     * Apply content-aware chunking strategies to optimize text segmentation.
     * Uses multiple chunking approaches and selects the most appropriate one.
     *
     * @param fullText The complete text to chunk
     * @return List of optimally sized text chunks
     */
    private List<String> chunkTextContentAware(String fullText) {
        List<String> paragraphChunks = chunkByParagraphs(fullText);
        if (isOptimalChunkSize(paragraphChunks)) {
            return paragraphChunks;
        }

        List<String> sentenceChunks = chunkBySentences(fullText);
        if (isOptimalChunkSize(sentenceChunks)) {
            return sentenceChunks;
        }

        return chunkBySemanticSlidingWindow(fullText);
    }

    /**
     * Chunk text by paragraphs with intelligent overlap for context preservation.
     */
    private List<String> chunkByParagraphs(String text) {
        List<String> chunks = new ArrayList<>();
        String[] paragraphs = text.split("\\n\\s*\\n");
        StringBuilder currentChunk = new StringBuilder();

        for (String paragraph : paragraphs) {
            String trimmedParagraph = paragraph.trim();
            if (trimmedParagraph.isEmpty()) continue;

            if (currentChunk.length() + trimmedParagraph.length() + 2 > MAX_CHUNK_SIZE) {
                if (currentChunk.length() >= MIN_CHUNK_SIZE) {
                    chunks.add(currentChunk.toString().trim());
                    String overlap = getLastSentences(currentChunk.toString(), DEFAULT_OVERLAP);
                    currentChunk = new StringBuilder(overlap.isEmpty() ? "" : overlap + " ");
                } else {
                    currentChunk = new StringBuilder();
                }
            }

            currentChunk.append(trimmedParagraph).append("\n\n");
        }

        if (currentChunk.length() >= MIN_CHUNK_SIZE) {
            chunks.add(currentChunk.toString().trim());
        }

        return chunks;
    }

    /**
     * Chunk text by sentences with intelligent overlap for semantic continuity.
     */
    private List<String> chunkBySentences(String text) {
        List<String> chunks = new ArrayList<>();
        String[] sentences = text.split("(?<=[.!?])\\s+");
        StringBuilder currentChunk = new StringBuilder();

        for (String sentence : sentences) {
            String trimmedSentence = sentence.trim();
            if (trimmedSentence.isEmpty()) continue;

            if (currentChunk.length() + trimmedSentence.length() + 1 > MAX_CHUNK_SIZE) {
                if (currentChunk.length() >= MIN_CHUNK_SIZE) {
                    chunks.add(currentChunk.toString().trim());
                    String overlap = getLastSentences(currentChunk.toString(), DEFAULT_OVERLAP);
                    currentChunk = new StringBuilder(overlap.isEmpty() ? "" : overlap + " ");
                } else {
                    currentChunk = new StringBuilder();
                }
            }

            currentChunk.append(trimmedSentence).append(" ");
        }

        if (currentChunk.length() >= MIN_CHUNK_SIZE) {
            chunks.add(currentChunk.toString().trim());
        }

        return chunks;
    }

    /**
     * Apply semantic sliding window chunking for optimal text segmentation.
     * Preserves sentence boundaries while maintaining consistent chunk sizes.
     */
    private List<String> chunkBySemanticSlidingWindow(String text) {
        List<String> chunks = new ArrayList<>();
        int start = 0;

        while (start < text.length()) {
            int end = Math.min(start + DEFAULT_CHUNK_SIZE, text.length());

            if (end < text.length()) {
                int lastSentenceEnd = Math.max(
                    Math.max(text.lastIndexOf('.', end), text.lastIndexOf('?', end)),
                    text.lastIndexOf('!', end)
                );
                if (lastSentenceEnd > start + MIN_CHUNK_SIZE) {
                    end = lastSentenceEnd + 1;
                }
            }

            String chunk = text.substring(start, end).trim();
            if (!chunk.isEmpty() && chunk.length() >= MIN_CHUNK_SIZE) {
                chunks.add(chunk);
            }

            start = Math.max(start + DEFAULT_CHUNK_SIZE - DEFAULT_OVERLAP, end);
        }

        return chunks;
    }

    /**
     * Evaluate if chunk sizes are optimal for processing.
     */
    private boolean isOptimalChunkSize(List<String> chunks) {
        if (chunks.isEmpty()) return false;
        double avgLength = chunks.stream().mapToInt(String::length).average().orElse(0);
        return avgLength >= MIN_CHUNK_SIZE && avgLength <= MAX_CHUNK_SIZE;
    }

    /**
     * Extract the last few sentences up to a maximum length for overlap preservation.
     */
    private String getLastSentences(String text, int maxLength) {
        String[] sentences = text.split("(?<=[.!?])\\s+");
        StringBuilder result = new StringBuilder();

        for (int i = sentences.length - 1; i >= 0; i--) {
            if (result.length() + sentences[i].length() > maxLength) {
                break;
            }
            result.insert(0, sentences[i] + " ");
        }

        return result.toString().trim();
    }





    /**
     * Cleanup method to properly shutdown the ExecutorService when the service is destroyed.
     */
    @PreDestroy
    public void cleanup() {
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * Extract file extension from filename.
     */
    private String getFileExtension(String filename) {
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1) {
            return "";
        }
        return filename.substring(lastDotIndex + 1);
    }

    /**
     * Extract filename without extension for document labeling.
     */
    private String getFileNameWithoutExtension(String filename) {
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1) {
            return filename;
        }
        return filename.substring(0, lastDotIndex);
    }
}
