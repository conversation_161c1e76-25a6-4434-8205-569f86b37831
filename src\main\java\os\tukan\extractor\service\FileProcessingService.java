package os.tukan.extractor.service;

import org.apache.pdfbox.Loader;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PreDestroy;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@Service
public class FileProcessingService {

    private static final int DEFAULT_CHUNK_SIZE = 1000;
    private static final int DEFAULT_OVERLAP = 100;
    private static final int MAX_CHUNK_SIZE = 2000;
    private static final int MIN_CHUNK_SIZE = 200;
    private static final long MAX_FILE_SIZE = 100L * 1024 * 1024;
    private static final int PROCESSING_TIMEOUT_SECONDS = 30;

    private final ExecutorService executorService;

    public FileProcessingService() {
        int threadCount = Math.max(2, Math.min(Runtime.getRuntime().availableProcessors(), 8));
        this.executorService = Executors.newFixedThreadPool(threadCount, r -> {
            Thread t = new Thread(r, "pdf-processor-" + System.currentTimeMillis());
            t.setDaemon(true);
            return t;
        });
    }

    public List<String> processDatasetFile(MultipartFile file) throws IOException {
        if (file == null || file.isEmpty() || file.getOriginalFilename() == null) {
            throw new IOException("Invalid PDF file provided");
        }

        String extension = getFileExtension(file.getOriginalFilename()).toLowerCase();
        if (!"pdf".equals(extension)) {
            throw new IOException("Only PDF files are supported. Provided file type: " + extension);
        }

        return processPdfDataset(file);
    }

    public List<String> processMultipleDatasetFiles(MultipartFile[] files) throws IOException {
        return processMultipleDatasetFiles(files, false);
    }

    public List<String> processMultipleDatasetFiles(MultipartFile[] files, boolean directoryMode) throws IOException {
        long startTime = System.currentTimeMillis();

        if (files == null || files.length == 0) {
            throw new IOException("No PDF files provided");
        }

        List<MultipartFile> pdfFiles = Arrays.stream(files)
            .filter(file -> file != null && !file.isEmpty() && file.getOriginalFilename() != null)
            .filter(file -> "pdf".equals(getFileExtension(file.getOriginalFilename()).toLowerCase()))
            .filter(file -> file.getSize() <= MAX_FILE_SIZE)
            .collect(Collectors.toList());

        if (pdfFiles.isEmpty()) {
            String message = directoryMode ?
                "No PDF files found in the selected directory. Only PDF files are supported." :
                "No valid PDF files provided. Only PDF files are supported.";
            throw new IOException(message);
        }

        List<CompletableFuture<List<String>>> futures = pdfFiles.parallelStream()
            .map(file -> CompletableFuture.supplyAsync(() -> {
                try {
                    List<String> documents = processPdfDataset(file);
                    String filename = getFileNameWithoutExtension(file.getOriginalFilename());
                    return documents.stream()
                        .map(doc -> String.format("[%s] %s", filename, doc))
                        .collect(Collectors.toList());
                } catch (Exception e) {
                    return new ArrayList<String>();
                }
            }, executorService))
            .collect(Collectors.toList());

        List<String> allDocuments = futures.parallelStream()
            .map(future -> {
                try {
                    return future.get(PROCESSING_TIMEOUT_SECONDS, TimeUnit.SECONDS);
                } catch (Exception e) {
                    return new ArrayList<String>();
                }
            })
            .flatMap(List::stream)
            .collect(Collectors.toList());

        if (allDocuments.isEmpty()) {
            throw new IOException("No PDF documents could be processed successfully");
        }

        long processingTime = System.currentTimeMillis() - startTime;
        if (processingTime > 5000) { // Only log if processing takes more than 5 seconds
            System.out.printf("Processed %d PDFs in %dms%n", pdfFiles.size(), processingTime);
        }

        return allDocuments;
    }
    private List<String> processPdfDataset(MultipartFile file) throws IOException {
        try (InputStream inputStream = file.getInputStream()) {
            byte[] pdfBytes = inputStream.readAllBytes();

            try (PDDocument document = Loader.loadPDF(pdfBytes)) {
                PDFTextStripper textStripper = new PDFTextStripper();
                textStripper.setSortByPosition(true);

                String fullText = textStripper.getText(document);

                // Clear reference to help GC
                pdfBytes = null;

                return chunkTextContentAware(fullText);
            }
        } catch (IOException e) {
            throw new IOException("Failed to process PDF: " + e.getMessage(), e);
        }
    }

    private List<String> chunkTextContentAware(String fullText) {
        if (fullText == null || fullText.trim().isEmpty()) {
            return new ArrayList<>();
        }

        // Try paragraph-based chunking first for better semantic coherence
        List<String> paragraphChunks = chunkByParagraphs(fullText);
        if (isOptimalChunkSize(paragraphChunks)) {
            return paragraphChunks;
        }

        // Fall back to sentence-based chunking
        List<String> sentenceChunks = chunkBySentences(fullText);
        if (isOptimalChunkSize(sentenceChunks)) {
            return sentenceChunks;
        }

        // Last resort: sliding window approach
        return chunkBySemanticSlidingWindow(fullText);
    }

    private List<String> chunkByParagraphs(String text) {
        List<String> chunks = new ArrayList<>();
        String[] paragraphs = text.split("\\n\\s*\\n");
        StringBuilder currentChunk = new StringBuilder();

        for (String paragraph : paragraphs) {
            String trimmedParagraph = paragraph.trim();
            if (trimmedParagraph.isEmpty() || isHeaderOrFooter(trimmedParagraph)) continue;

            int projectedLength = currentChunk.length() + trimmedParagraph.length() + 2;

            if (projectedLength > MAX_CHUNK_SIZE && currentChunk.length() >= MIN_CHUNK_SIZE) {
                chunks.add(currentChunk.toString().trim());
                String overlap = getLastSentences(currentChunk.toString(), DEFAULT_OVERLAP);
                currentChunk = new StringBuilder(overlap.isEmpty() ? "" : overlap + " ");
            }

            currentChunk.append(trimmedParagraph).append("\n\n");
        }

        if (currentChunk.length() >= MIN_CHUNK_SIZE) {
            chunks.add(currentChunk.toString().trim());
        }

        return chunks.isEmpty() ? List.of(text.trim()) : chunks;
    }

    private List<String> chunkBySentences(String text) {
        List<String> chunks = new ArrayList<>();
        String[] sentences = text.split("(?<=[.!?])\\s+");
        StringBuilder currentChunk = new StringBuilder();

        for (String sentence : sentences) {
            String trimmedSentence = sentence.trim();
            if (trimmedSentence.isEmpty() || trimmedSentence.length() < 10) continue;

            int projectedLength = currentChunk.length() + trimmedSentence.length() + 1;

            if (projectedLength > MAX_CHUNK_SIZE && currentChunk.length() >= MIN_CHUNK_SIZE) {
                chunks.add(currentChunk.toString().trim());
                String overlap = getLastSentences(currentChunk.toString(), DEFAULT_OVERLAP);
                currentChunk = new StringBuilder(overlap.isEmpty() ? "" : overlap + " ");
            }

            currentChunk.append(trimmedSentence).append(" ");
        }

        if (currentChunk.length() >= MIN_CHUNK_SIZE) {
            chunks.add(currentChunk.toString().trim());
        }

        return chunks.isEmpty() ? List.of(text.trim()) : chunks;
    }

    private List<String> chunkBySemanticSlidingWindow(String text) {
        List<String> chunks = new ArrayList<>();
        int start = 0;

        while (start < text.length()) {
            int end = Math.min(start + DEFAULT_CHUNK_SIZE, text.length());

            if (end < text.length()) {
                int lastSentenceEnd = Math.max(
                    Math.max(text.lastIndexOf('.', end), text.lastIndexOf('?', end)),
                    text.lastIndexOf('!', end)
                );
                if (lastSentenceEnd > start + MIN_CHUNK_SIZE) {
                    end = lastSentenceEnd + 1;
                }
            }

            String chunk = text.substring(start, end).trim();
            if (!chunk.isEmpty() && chunk.length() >= MIN_CHUNK_SIZE) {
                chunks.add(chunk);
            }

            start = Math.max(start + DEFAULT_CHUNK_SIZE - DEFAULT_OVERLAP, end);
        }

        return chunks;
    }

    private boolean isOptimalChunkSize(List<String> chunks) {
        if (chunks.isEmpty()) return false;
        double avgLength = chunks.stream().mapToInt(String::length).average().orElse(0);
        return avgLength >= MIN_CHUNK_SIZE && avgLength <= MAX_CHUNK_SIZE;
    }

    private String getLastSentences(String text, int maxLength) {
        String[] sentences = text.split("(?<=[.!?])\\s+");
        StringBuilder result = new StringBuilder();

        for (int i = sentences.length - 1; i >= 0; i--) {
            if (result.length() + sentences[i].length() > maxLength) {
                break;
            }
            result.insert(0, sentences[i] + " ");
        }

        return result.toString().trim();
    }

    private boolean isHeaderOrFooter(String text) {
        if (text.length() < 5 || text.length() > 100) return false;

        String lower = text.toLowerCase();
        return lower.matches(".*page\\s+\\d+.*") ||
               lower.matches(".*\\d+\\s*$") ||
               lower.startsWith("chapter ") ||
               lower.startsWith("section ") ||
               text.matches("^[A-Z\\s]{3,}$") ||
               lower.contains("copyright") ||
               lower.contains("©");
    }

    @PreDestroy
    public void cleanup() {
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    private String getFileExtension(String filename) {
        int lastDotIndex = filename.lastIndexOf('.');
        return lastDotIndex == -1 ? "" : filename.substring(lastDotIndex + 1);
    }

    private String getFileNameWithoutExtension(String filename) {
        int lastDotIndex = filename.lastIndexOf('.');
        return lastDotIndex == -1 ? filename : filename.substring(0, lastDotIndex);
    }
}
