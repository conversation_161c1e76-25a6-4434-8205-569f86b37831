import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ComparisonResult, ComparisonMetrics } from '../../models/benchmark.models';
import { ComparisonService } from '../../services/comparison.service';

@Component({
  selector: 'app-results-comparison',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './results-comparison.component.html',
  styleUrls: ['./results-comparison.component.scss']
})
export class ResultsComparisonComponent implements OnInit {
  @Input() comparison: ComparisonResult | null = null;

  expandedQueries = new Set<string>();

  constructor(private comparisonService: ComparisonService) {}

  ngOnInit(): void {}

  toggleQueryExpansion(query: string): void {
    if (this.expandedQueries.has(query)) {
      this.expandedQueries.delete(query);
    } else {
      this.expandedQueries.add(query);
    }
  }

  isQueryExpanded(query: string): boolean {
    return this.expandedQueries.has(query);
  }

  formatPercentage(value: number): string {
    return (value * 100).toFixed(1) + '%';
  }

  formatTime(value: number): string {
    return value.toFixed(1) + 'ms';
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  formatDifference(value: number, isTime: boolean = false): string {
    if (isTime) {
      return this.comparisonService.formatTimeDiff(value);
    } else {
      return this.comparisonService.formatPercentageDiff(value);
    }
  }

  getTrendClass(value: number, isTime: boolean = false): string {
    const trend = isTime 
      ? this.comparisonService.getTimeTrend(value)
      : this.comparisonService.getMetricTrend(value);
    
    return `trend-${trend}`;
  }

  getTrendIcon(value: number, isTime: boolean = false): string {
    const trend = isTime 
      ? this.comparisonService.getTimeTrend(value)
      : this.comparisonService.getMetricTrend(value);
    
    switch (trend) {
      case 'positive': return 'fas fa-arrow-up';
      case 'negative': return 'fas fa-arrow-down';
      default: return 'fas fa-minus';
    }
  }

  getModelDisplayName(modelInfo: any): string {
    console.log(modelInfo);
    return modelInfo?.name || modelInfo?.modelName || 'Unknown Model';
  }

  getRankingChangeIcon(rankDiff: number): string {
    if (rankDiff > 0) return 'fas fa-arrow-down text-red-500';
    if (rankDiff < 0) return 'fas fa-arrow-up text-green-500';
    return 'fas fa-minus text-gray-400';
  }

  getRankingChangeText(primaryRank: number, secondaryRank: number): string {
    if (primaryRank === -1) return 'Not found → Rank ' + secondaryRank;
    if (secondaryRank === -1) return 'Rank ' + primaryRank + ' → Not found';
    return `Rank ${primaryRank} → Rank ${secondaryRank}`;
  }

  getSignificantRankingChanges(queryComparison: any): any[] {
    return queryComparison.rankingChanges
      .filter((change: any) => Math.abs(change.rankDifference) > 0)
      .slice(0, 5); // Show top 5 most significant changes
  }

  downloadComparison(): void {
    if (!this.comparison) return;

    const primaryModel = this.getModelDisplayName(this.comparison.primary.modelInfo);
    const secondaryModel = this.getModelDisplayName(this.comparison.secondary.modelInfo);

    const data = {
      comparisonDate: new Date().toISOString(),
      primary: {
        model: primaryModel,
        jobId: this.comparison.primary.jobId,
        label: this.comparison.primary.label,
        metrics: this.comparison.primary.overallMetrics
      },
      secondary: {
        model: secondaryModel,
        jobId: this.comparison.secondary.jobId,
        label: this.comparison.secondary.label,
        metrics: this.comparison.secondary.overallMetrics
      },
      overallComparison: this.comparison.overallComparison,
      queryComparisons: this.comparison.queryComparisons.map(qc => ({
        query: qc.query,
        primaryMetrics: {
          precision: qc.primary.precision,
          recall: qc.primary.recall,
          f1: qc.primary.f1,
          ndcg: qc.primary.ndcg,
          mrr: qc.primary.mrr
        },
        secondaryMetrics: {
          precision: qc.secondary.precision,
          recall: qc.secondary.recall,
          f1: qc.secondary.f1,
          ndcg: qc.secondary.ndcg,
          mrr: qc.secondary.mrr
        },
        differences: qc.metrics,
        significantRankingChanges: this.getSignificantRankingChanges(qc)
      }))
    };

    const dataStr = JSON.stringify(data, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);

    const link = document.createElement('a');
    link.href = url;
    const filename = `comparison-${primaryModel.replace(/\s+/g, '-')}-vs-${secondaryModel.replace(/\s+/g, '-')}-${new Date().toISOString().split('T')[0]}.json`;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    URL.revokeObjectURL(url);
  }
}
