<div *ngIf="comparison" class="results-comparison">
  <div class="comparison-header">
    <h3 class="title">
      <i class="fas fa-balance-scale mr-2"></i>
      Model Comparison Results
    </h3>
    
    <button 
      class="btn btn-download"
      (click)="downloadComparison()"
      title="Download comparison results">
      <i class="fas fa-download"></i>
      Download
    </button>
  </div>

  <!-- Model Information -->
  <div class="models-overview">
    <div class="model-card primary">
      <div class="model-header">
        <span class="model-badge">Primary</span>
        <h4 class="model-name">{{ getModelDisplayName(comparison.primary.modelInfo) }}</h4>
      </div>
      <div class="model-meta">
        <span *ngIf="comparison.primary.label" class="model-label">{{ comparison.primary.label }}</span>
        <span class="model-id">{{ comparison.primary.jobId.substring(0, 8) }}</span>
      </div>
    </div>

    <div class="vs-divider">
      <span>VS</span>
    </div>

    <div class="model-card secondary">
      <div class="model-header">
        <span class="model-badge">Secondary</span>
        <h4 class="model-name">{{ getModelDisplayName(comparison.secondary.modelInfo) }}</h4>
      </div>
      <div class="model-meta">
        <span *ngIf="comparison.secondary.label" class="model-label">{{ comparison.secondary.label }}</span>
        <span class="model-id">{{ comparison.secondary.jobId.substring(0, 8) }}</span>
      </div>
    </div>
  </div>

  <!-- Overall Metrics Comparison -->
  <div class="overall-comparison">
    <h4 class="section-title">Overall Performance Comparison</h4>
    
    <div class="metrics-comparison-grid">
      <!-- Precision -->
      <div class="metric-comparison">
        <div class="metric-name">Precision</div>
        <div class="metric-values">
          <div class="primary-value">{{ formatPercentage(comparison.primary.overallMetrics?.avgPrecision || 0) }}</div>
          <div class="secondary-value">{{ formatPercentage(comparison.secondary.overallMetrics?.avgPrecision || 0) }}</div>
        </div>
        <div class="metric-difference" [class]="getTrendClass(comparison.overallComparison.precisionDiff)">
          <i [class]="getTrendIcon(comparison.overallComparison.precisionDiff)"></i>
          {{ formatDifference(comparison.overallComparison.precisionDiff) }}
        </div>
      </div>

      <!-- Recall -->
      <div class="metric-comparison">
        <div class="metric-name">Recall</div>
        <div class="metric-values">
          <div class="primary-value">{{ formatPercentage(comparison.primary.overallMetrics?.avgRecall || 0) }}</div>
          <div class="secondary-value">{{ formatPercentage(comparison.secondary.overallMetrics?.avgRecall || 0) }}</div>
        </div>
        <div class="metric-difference" [class]="getTrendClass(comparison.overallComparison.recallDiff)">
          <i [class]="getTrendIcon(comparison.overallComparison.recallDiff)"></i>
          {{ formatDifference(comparison.overallComparison.recallDiff) }}
        </div>
      </div>

      <!-- F1 Score -->
      <div class="metric-comparison">
        <div class="metric-name">F1 Score</div>
        <div class="metric-values">
          <div class="primary-value">{{ formatPercentage(comparison.primary.overallMetrics?.avgF1 || 0) }}</div>
          <div class="secondary-value">{{ formatPercentage(comparison.secondary.overallMetrics?.avgF1 || 0) }}</div>
        </div>
        <div class="metric-difference" [class]="getTrendClass(comparison.overallComparison.f1Diff)">
          <i [class]="getTrendIcon(comparison.overallComparison.f1Diff)"></i>
          {{ formatDifference(comparison.overallComparison.f1Diff) }}
        </div>
      </div>

      <!-- nDCG -->
      <div class="metric-comparison">
        <div class="metric-name">nDCG</div>
        <div class="metric-values">
          <div class="primary-value">{{ formatPercentage(comparison.primary.overallMetrics?.avgNdcg || 0) }}</div>
          <div class="secondary-value">{{ formatPercentage(comparison.secondary.overallMetrics?.avgNdcg || 0) }}</div>
        </div>
        <div class="metric-difference" [class]="getTrendClass(comparison.overallComparison.ndcgDiff)">
          <i [class]="getTrendIcon(comparison.overallComparison.ndcgDiff)"></i>
          {{ formatDifference(comparison.overallComparison.ndcgDiff) }}
        </div>
      </div>

      <!-- MRR -->
      <div class="metric-comparison">
        <div class="metric-name">MRR</div>
        <div class="metric-values">
          <div class="primary-value">{{ formatPercentage(comparison.primary.overallMetrics?.avgMrr || 0) }}</div>
          <div class="secondary-value">{{ formatPercentage(comparison.secondary.overallMetrics?.avgMrr || 0) }}</div>
        </div>
        <div class="metric-difference" [class]="getTrendClass(comparison.overallComparison.mrrDiff)">
          <i [class]="getTrendIcon(comparison.overallComparison.mrrDiff)"></i>
          {{ formatDifference(comparison.overallComparison.mrrDiff) }}
        </div>
      </div>

      <!-- Query Time -->
      <div class="metric-comparison">
        <div class="metric-name">Avg Query Time</div>
        <div class="metric-values">
          <div class="primary-value">{{ formatTime(comparison.primary.overallMetrics?.avgQueryTimeMs || 0) }}</div>
          <div class="secondary-value">{{ formatTime(comparison.secondary.overallMetrics?.avgQueryTimeMs || 0) }}</div>
        </div>
        <div class="metric-difference" [class]="getTrendClass(comparison.overallComparison.queryTimeDiff, true)">
          <i [class]="getTrendIcon(comparison.overallComparison.queryTimeDiff, true)"></i>
          {{ formatDifference(comparison.overallComparison.queryTimeDiff, true) }}
        </div>
      </div>

      <!-- Dataset Size -->
      <div class="metric-comparison">
        <div class="metric-name">Dataset Size</div>
        <div class="metric-values">
          <div class="primary-value">{{ formatFileSize(comparison.primary.overallMetrics?.datasetSizeBytes || 0) }}</div>
          <div class="secondary-value">{{ formatFileSize(comparison.secondary.overallMetrics?.datasetSizeBytes || 0) }}</div>
        </div>
        <div class="metric-difference neutral">
          <i class="fas fa-info-circle"></i>
          <span *ngIf="(comparison.primary.overallMetrics?.datasetSizeBytes || 0) === (comparison.secondary.overallMetrics?.datasetSizeBytes || 0)">Same</span>
          <span *ngIf="(comparison.primary.overallMetrics?.datasetSizeBytes || 0) !== (comparison.secondary.overallMetrics?.datasetSizeBytes || 0)">Different</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Query-Level Comparison -->
  <div class="query-comparison-section">
    <h4 class="section-title">Query-Level Comparison</h4>
    
    <div class="query-list">
      <div 
        *ngFor="let queryComp of comparison.queryComparisons; let i = index" 
        class="query-item">
        
        <div class="query-header" (click)="toggleQueryExpansion(queryComp.query)">
          <div class="query-info">
            <span class="query-text">{{ queryComp.query }}</span>
            <div class="query-summary">
              <span class="metric-summary">
                P: {{ formatPercentage(queryComp.primary.precision) }} → {{ formatPercentage(queryComp.secondary.precision) }}
              </span>
              <span class="metric-summary">
                R: {{ formatPercentage(queryComp.primary.recall) }} → {{ formatPercentage(queryComp.secondary.recall) }}
              </span>
              <span class="metric-summary">
                nDCG: {{ formatPercentage(queryComp.primary.ndcg) }} → {{ formatPercentage(queryComp.secondary.ndcg) }}
              </span>
            </div>
          </div>
          
          <div class="query-controls">
            <i class="fas" [class.fa-chevron-down]="!isQueryExpanded(queryComp.query)" 
               [class.fa-chevron-up]="isQueryExpanded(queryComp.query)"></i>
          </div>
        </div>

        <div *ngIf="isQueryExpanded(queryComp.query)" class="query-details">
          <!-- Detailed Metrics -->
          <div class="detailed-metrics">
            <div class="metrics-table">
              <div class="table-header">
                <div class="metric-col">Metric</div>
                <div class="primary-col">Primary</div>
                <div class="secondary-col">Secondary</div>
                <div class="diff-col">Difference</div>
              </div>
              
              <div class="table-row">
                <div class="metric-col">Precision</div>
                <div class="primary-col">{{ formatPercentage(queryComp.primary.precision) }}</div>
                <div class="secondary-col">{{ formatPercentage(queryComp.secondary.precision) }}</div>
                <div class="diff-col" [class]="getTrendClass(queryComp.metrics.precisionDiff)">
                  {{ formatDifference(queryComp.metrics.precisionDiff) }}
                </div>
              </div>
              
              <div class="table-row">
                <div class="metric-col">Recall</div>
                <div class="primary-col">{{ formatPercentage(queryComp.primary.recall) }}</div>
                <div class="secondary-col">{{ formatPercentage(queryComp.secondary.recall) }}</div>
                <div class="diff-col" [class]="getTrendClass(queryComp.metrics.recallDiff)">
                  {{ formatDifference(queryComp.metrics.recallDiff) }}
                </div>
              </div>
              
              <div class="table-row">
                <div class="metric-col">F1 Score</div>
                <div class="primary-col">{{ formatPercentage(queryComp.primary.f1) }}</div>
                <div class="secondary-col">{{ formatPercentage(queryComp.secondary.f1) }}</div>
                <div class="diff-col" [class]="getTrendClass(queryComp.metrics.f1Diff)">
                  {{ formatDifference(queryComp.metrics.f1Diff) }}
                </div>
              </div>
              
              <div class="table-row">
                <div class="metric-col">nDCG</div>
                <div class="primary-col">{{ formatPercentage(queryComp.primary.ndcg) }}</div>
                <div class="secondary-col">{{ formatPercentage(queryComp.secondary.ndcg) }}</div>
                <div class="diff-col" [class]="getTrendClass(queryComp.metrics.ndcgDiff)">
                  {{ formatDifference(queryComp.metrics.ndcgDiff) }}
                </div>
              </div>
              
              <div class="table-row">
                <div class="metric-col">MRR</div>
                <div class="primary-col">{{ formatPercentage(queryComp.primary.mrr) }}</div>
                <div class="secondary-col">{{ formatPercentage(queryComp.secondary.mrr) }}</div>
                <div class="diff-col" [class]="getTrendClass(queryComp.metrics.mrrDiff)">
                  {{ formatDifference(queryComp.metrics.mrrDiff) }}
                </div>
              </div>
            </div>
          </div>

          <!-- Ranking Changes -->
          <div *ngIf="getSignificantRankingChanges(queryComp).length > 0" class="ranking-changes">
            <h5>Significant Ranking Changes</h5>
            <div class="ranking-changes-list">
              <div 
                *ngFor="let change of getSignificantRankingChanges(queryComp)" 
                class="ranking-change-item">
                <div class="document-id">{{ change.documentId }}</div>
                <div class="ranking-change">
                  <i [class]="getRankingChangeIcon(change.rankDifference)"></i>
                  {{ getRankingChangeText(change.primaryRank, change.secondaryRank) }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div *ngIf="!comparison" class="no-comparison">
  <i class="fas fa-chart-line text-4xl mb-4"></i>
  <p>No comparison available. Select two results to compare.</p>
</div>
