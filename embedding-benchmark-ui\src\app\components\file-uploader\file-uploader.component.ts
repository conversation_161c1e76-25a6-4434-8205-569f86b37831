import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-file-uploader',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './file-uploader.component.html',
  styleUrls: ['./file-uploader.component.scss']
})
export class FileUploaderComponent {
  @Input() title: string = '';
  @Input() description: string = '';
  @Input() acceptedTypes: string = '';
  @Input() uploadedFileName: string = '';
  @Input() allowMultiple: boolean = false;
  @Input() allowDirectory: boolean = false;
  @Output() fileUpload = new EventEmitter<File>();
  @Output() multipleFileUpload = new EventEmitter<{files: File[], directoryMode: boolean}>();

  isDragOver = false;
  uploading = false;
  error = '';
  uploadedFiles: File[] = [];

  onDragOver(event: DragEvent) {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOver = true;
  }

  onDragLeave(event: DragEvent) {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOver = false;
  }

  onDrop(event: DragEvent) {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOver = false;

    const files = event.dataTransfer?.files;
    if (files && files.length > 0) {
      if (this.allowMultiple) {
        // For drag/drop, we can't detect directory mode, so default to false
        this.handleMultipleFiles(Array.from(files), false);
      } else {
        this.handleFile(files[0]);
      }
    }
  }

  onFileSelected(event: any) {
    const files = event.target.files;
    if (files && files.length > 0) {
      if (this.allowMultiple) {
        // Detect if this is a directory upload by checking if webkitdirectory was used
        const isDirectoryUpload = event.target.webkitdirectory;
        this.handleMultipleFiles(Array.from(files), isDirectoryUpload);
      } else {
        this.handleFile(files[0]);
      }
    }
  }

  private handleFile(file: File) {
    this.error = '';

    // Validate file type
    if (this.acceptedTypes && !this.isFileTypeAccepted(file)) {
      this.error = `File type not supported. Accepted types: ${this.acceptedTypes}`;
      return;
    }

    // Validate file size (max 100MB)
    const maxSize = 100 * 1024 * 1024; // 100MB
    if (file.size > maxSize) {
      this.error = 'File size too large. Maximum size is 100MB.';
      return;
    }

    this.uploading = true;
    this.fileUpload.emit(file);

    // Reset uploading state after a delay (will be handled by parent component)
    setTimeout(() => {
      this.uploading = false;
    }, 1000);
  }

  private handleMultipleFiles(files: File[], directoryMode: boolean = false) {
    this.error = '';
    const validFiles: File[] = [];
    const maxSize = 100 * 1024 * 1024; // 100MB per file

    for (const file of files) {
      // Validate file type
      if (this.acceptedTypes && !this.isFileTypeAccepted(file)) {
        this.error = `File ${file.name} type not supported. Accepted types: ${this.acceptedTypes}`;
        continue;
      }

      // Validate file size
      if (file.size > maxSize) {
        this.error = `File ${file.name} is too large. Maximum size is 100MB per file.`;
        continue;
      }

      validFiles.push(file);
    }

    if (validFiles.length === 0) {
      this.error = 'No valid files found.';
      return;
    }

    this.uploadedFiles = validFiles;
    this.uploading = true;
    this.multipleFileUpload.emit({ files: validFiles, directoryMode });

    // Reset uploading state after a delay
    setTimeout(() => {
      this.uploading = false;
    }, 2000);
  }

  private isFileTypeAccepted(file: File): boolean {
    if (!this.acceptedTypes) return true;

    const acceptedExtensions = this.acceptedTypes.split(',').map(ext => ext.trim());
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();

    return acceptedExtensions.some(ext =>
      ext === fileExtension ||
      ext === file.type ||
      (ext.startsWith('.') && fileExtension === ext)
    );
  }
}
