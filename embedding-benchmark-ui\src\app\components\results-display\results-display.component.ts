import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BenchmarkResult, BenchmarkStatus } from '../../models/benchmark.models';

@Component({
  selector: 'app-results-display',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './results-display.component.html',
  styleUrls: ['./results-display.component.scss']
})
export class ResultsDisplayComponent {
  @Input() result: BenchmarkResult | null = null;
  @Input() isLoading = false;

  BenchmarkStatus = BenchmarkStatus;

  downloadResults() {
    if (!this.result) return;

    const dataStr = JSON.stringify(this.result, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);

    const link = document.createElement('a');
    link.href = url;
    link.download = `benchmark-results-${this.result.jobId}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    URL.revokeObjectURL(url);
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  getEmbeddingEfficiencyText(): string {
    if (!this.result?.overallMetrics?.embeddingTimeMetrics) {
      return 'No embedding time data available';
    }

    const metrics = this.result.overallMetrics.embeddingTimeMetrics;
    const avgDocTime = metrics.avgDocumentEmbeddingTimeMs;
    const avgQueryTime = metrics.avgQueryEmbeddingTimeMs;

    // Categorize performance based on average times
    let docEfficiency: string;
    let queryEfficiency: string;

    if (avgDocTime < 100) {
      docEfficiency = 'Very Fast';
    } else if (avgDocTime < 500) {
      docEfficiency = 'Fast';
    } else if (avgDocTime < 1000) {
      docEfficiency = 'Moderate';
    } else {
      docEfficiency = 'Slow';
    }

    if (avgQueryTime < 50) {
      queryEfficiency = 'Very Fast';
    } else if (avgQueryTime < 200) {
      queryEfficiency = 'Fast';
    } else if (avgQueryTime < 500) {
      queryEfficiency = 'Moderate';
    } else {
      queryEfficiency = 'Slow';
    }

    return `Documents: ${docEfficiency} (${avgDocTime.toFixed(1)}ms avg), Queries: ${queryEfficiency} (${avgQueryTime.toFixed(1)}ms avg)`;
  }
}
