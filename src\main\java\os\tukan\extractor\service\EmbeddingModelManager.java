package os.tukan.extractor.service;

import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import os.tukan.extractor.config.EmbeddingModelConfig;
import os.tukan.extractor.model.EmbeddingResult;

import java.util.HashMap;
import java.util.Map;

@Log4j2
@Service
public class EmbeddingModelManager {

    // Remove the shared provider to prevent config overwriting
    private final Map<String, EmbeddingProvider> providerCache = new HashMap<>();

    public EmbeddingModelManager() {
        // No longer inject a shared provider
    }

    public float[] generateEmbeddingOnly(String text, EmbeddingModelConfig config) throws Exception {
        EmbeddingProvider provider = getProvider(config);
        return provider.embed(text);
    }

    /**
     * Get an embedding provider for the given model configuration
     * Creates separate provider instances to prevent config overwriting
     */
    public EmbeddingProvider getProvider(EmbeddingModelConfig config) {
        if (config == null) {
            throw new IllegalArgumentException("Embedding model config cannot be null");
        }

        String cacheKey = config.getId();

        if (providerCache.containsKey(cacheKey)) {
            return providerCache.get(cacheKey);
        }

        EmbeddingProvider provider;

        if (config.getProvider().equalsIgnoreCase("ollama")) {
            OllamaEmbeddingProvider ollamaProvider = new OllamaEmbeddingProvider();
            ollamaProvider.setConfig(config);
            provider = ollamaProvider;
        } else {
            throw new IllegalArgumentException("Unsupported embedding provider: " + config.getProvider());
        }

        providerCache.put(cacheKey, provider);
        return provider;
    }



    public EmbeddingResult generateEmbeddingWithTiming(String text, EmbeddingModelConfig config) throws Exception {

        EmbeddingProvider provider = getProvider(config);

        for (int i = 0; i < 3; i++) {
            provider.embed("warmup text " + i);
        }

        // Additional warmup with similar length text to actual input to eliminate network latency
        String warmupText = text.length() > 50 ? text.substring(0, 50) : "warmup text with similar length";
        provider.embed(warmupText);

        // Measure only the actual embedding computation time, excluding all external factors
        long start = System.nanoTime();
        float[] embedding = provider.embed(text);
        long durationMs = (System.nanoTime() - start) / 1_000_000;

        return new EmbeddingResult(embedding, durationMs, config.getModelName(), config.getProvider());
    }


    public void clearCache() {
        providerCache.clear();
    }

}
