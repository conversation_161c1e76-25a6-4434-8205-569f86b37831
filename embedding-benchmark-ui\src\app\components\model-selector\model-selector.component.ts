import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { BenchmarkApiService } from '../../services/benchmark-api.service';
import { EmbeddingModelConfig } from '../../models/benchmark.models';

@Component({
  selector: 'app-model-selector',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './model-selector.component.html',
  styleUrls: ['./model-selector.component.scss']
})
export class ModelSelectorComponent implements OnInit {
  @Input() selectedModel: string = '';
  @Output() modelChange = new EventEmitter<string>();

  models: EmbeddingModelConfig[] = [];
  loading = false;
  refreshing = false;
  error = '';

  constructor(private benchmarkApi: BenchmarkApiService) {}

  ngOnInit() {
    this.loadModels();
  }

  get selectedModelInfo(): EmbeddingModelConfig | undefined {
    return this.models.find(model => model.id === this.selectedModel);
  }

  loadModels(refresh: boolean = false) {
    if (refresh) {
      this.refreshing = true;
    } else {
      this.loading = true;
    }
    this.error = '';

    const apiCall = this.benchmarkApi.getAvailableModels();

    apiCall.subscribe({
      next: (models) => {
        this.models = models;
        this.loading = false;
        this.refreshing = false;
        if (models.length === 0) {
          this.error = 'No embedding models found. Please check if Ollama is running and has embedding models installed.';
        }
      },
      error: (error) => {
        this.handleModelLoadError(error);
      }
    });
  }

  private handleModelLoadError(error: any) {
    this.loading = false;
    this.refreshing = false;

    if (error.status === 503) {
      this.error = 'Ollama server is not available. Please ensure Ollama is running at the configured endpoint.';
    } else if (error.status === 0) {
      this.error = 'Cannot connect to the backend server. Please check if the backend is running.';
    } else {
      this.error = 'Failed to load models. Please check your connection and try again.';
    }

    console.error('Error loading models:', error);
  }

  onModelChange(modelId: string) {
    this.modelChange.emit(modelId);
  }

  formatModelSize(size?: number): string {
    if (!size) return 'Unknown size';

    const gb = size / (1024 * 1024 * 1024);
    if (gb >= 1) {
      return `${gb.toFixed(1)} GB`;
    }

    const mb = size / (1024 * 1024);
    return `${mb.toFixed(0)} MB`;
  }

  formatModifiedDate(modifiedAt?: string): string {
    if (!modifiedAt) return 'Unknown date';

    try {
      const date = new Date(modifiedAt);
      return date.toLocaleDateString();
    } catch {
      return 'Invalid date';
    }
  }
}
